import { Hono } from "hono";
import type { HonoEnv } from "../types/env";
import { Redis } from "@upstash/redis/cloudflare";
import {
  ApifyService,
  webhookResponseSchema,
  type YtDlpYouTubeMetadata,
} from "../services/apify";
import {
  YouTubeDataAPIService,
  type YouTubeApiMetadata,
  type YouTubeApiSubtitle,
} from "../services/youtube-api";
import { vValidator } from "@hono/valibot-validator";
import {
  LOCK_KEYS,
  VideoCacheService,
  type YtdlpVideoSubtitleListCache,
} from "../services/cache";
import { createMiddleware } from "hono/factory";
import { cache } from "hono/cache";
import { parseYouTubeAutoSub } from "../lib/subtitle/yt-json3";
import { lemonfoxJsonToWebVTT } from "../lib/subtitle/lemonfox";
import { pick } from "@std/collections";
import {
  createRevalidationService,
  type TaskExecutionOptions,
} from "../services/revalidate";
import { assertNever } from "@std/assert/unstable-never";
import { getLogger } from "@logtape/logtape";
import { retry } from "@std/async";
import { processYouTubeJson3Subtitle } from "../lib/subtitle/fetch-json3";

const logger = getLogger(["mx-api", "youtube"]);

// Extend the environment type to include our services
type ServiceVariables = {
  services: {
    apify: ApifyService;
    youtubeApi: YouTubeDataAPIService;
    cache: VideoCacheService;
  };
  createRevalidator: (
    videoId: string,
    options?: TaskExecutionOptions,
  ) => ReturnType<typeof createRevalidationService>;
};

type ExtendedHonoEnv = HonoEnv & {
  Variables: ServiceVariables;
};

// Create a middleware to initialize services
const servicesMiddleware = createMiddleware<ExtendedHonoEnv>(
  async (c, next) => {
    const redis = Redis.fromEnv(c.env);
    const apify = new ApifyService(c.env);
    const youtubeApi = new YouTubeDataAPIService(c.env);
    const cacheService = new VideoCacheService(redis);

    c.set("services", { apify, youtubeApi, cache: cacheService });
    c.set(
      "createRevalidator",
      (videoId: string, options?: TaskExecutionOptions) =>
        createRevalidationService(
          videoId,
          { youtubeApi, cache: cacheService, apify },
          c.executionCtx,
          options,
        ),
    );

    await next();
  },
);

type SubtitleResponse =
  | {
      type: "partial";
      subtitles: YouTubeApiSubtitle[];
    }
  | {
      type: "full";
      subtitles: YtdlpVideoSubtitleListCache;
    };

const app = new Hono<ExtendedHonoEnv>()
  .use(servicesMiddleware)
  .use(
    cache({
      cacheName: "mx/youtube",
      cacheControl: "max-age=3600",
      cacheableStatusCodes: [200],
    }),
  )
  .get("/video/:video_id/subtitles", async (c) => {
    const videoId = c.req.param("video_id");

    logger.info("Fetching video subtitles", { videoId });

    // Access services from context variables
    const { services, createRevalidator } = c.var;
    const revalidator = createRevalidator(videoId);

    // Check if video is cached as not found (404)
    const isNotFound = await services.cache.isNotFound(videoId);
    if (isNotFound) {
      logger.info("Video found in 404 tombstone cache", { videoId });
      return c.text("Not found", 404);
    }

    // Get both cached subtitle sources
    const [cachedSubtitles, cachedApiSubtitles] = await Promise.all([
      services.cache.getSubtitleList(videoId),
      services.cache.getYouTubeApiSubtitleList(videoId),
    ]);

    const cacheStatus = {
      ytDlpCached: !!cachedSubtitles,
      ytDlpStale: cachedSubtitles?.isStale ?? false,
      apiCached: !!cachedApiSubtitles,
      apiStale: cachedApiSubtitles?.isStale ?? false,
    };

    logger.debug("Cache status check completed", { videoId, cacheStatus });

    if (cachedSubtitles?.isStale) {
      logger.debug("Triggering yt-dlp revalidation", {
        videoId,
        reason: "stale_cache",
      });
      revalidator.revalidateWithYtDlp();
    }

    if (cachedApiSubtitles?.isStale) {
      logger.debug("Triggering API subtitles revalidation", {
        videoId,
        reason: "stale_cache",
      });
      revalidator.revalidateApiSubtitles();
    }

    // Return yt-dlp subtitles if available (complete data)
    if (cachedSubtitles?.data) {
      logger.info("Returning yt-dlp subtitles", {
        videoId,
        type: "full",
        subtitleCount: cachedSubtitles.data.length,
        fromCache: true,
      });

      return c.json(
        {
          type: "full",
          subtitles: cachedSubtitles.data,
        } satisfies SubtitleResponse,
        200,
        {
          // "Cache-Control": "public, max-age=7200, stale-while-revalidate=86400",
        },
      );
    }

    // Return cached YouTube API subtitles if available
    if (cachedApiSubtitles) {
      logger.info("Returning API subtitles", {
        videoId,
        type: "partial",
        subtitleCount: cachedApiSubtitles.data.length,
        fromCache: true,
      });

      return c.json(
        {
          type: "partial",
          subtitles: cachedApiSubtitles.data,
        } satisfies SubtitleResponse,
        200,
        {
          // "Cache-Control": "public, max-age=3600, stale-while-revalidate=86400",
        },
      );
    }

    logger.debug("No cached subtitles found, fetching from YouTube API", {
      videoId,
    });

    // No cached data - try to get YouTube API subtitle list immediately
    const youtubeSubtitles = await services.youtubeApi.getCaptionsList(videoId);

    if (!youtubeSubtitles) {
      logger.warn("Video not found", { videoId });
      return c.text("Not found", 404);
    }

    logger.debug("Background caching YouTube API subtitles", {
      videoId,
      subtitleCount: youtubeSubtitles.length,
    });
    // Cache YouTube API subtitle list without blocking
    c.executionCtx.waitUntil(
      services.cache.setYouTubeApiSubtitleList(videoId, youtubeSubtitles),
    );

    logger.debug("Triggering yt-dlp revalidation for subtitles", {
      videoId,
      reason: "no_cache",
    });
    revalidator.revalidateWithYtDlp();

    logger.info("Returning fresh API subtitles", {
      videoId,
      type: "partial",
      subtitleCount: youtubeSubtitles.length,
      fromCache: false,
    });

    // Return YouTube API subtitle list immediately
    return c.json(
      {
        type: "partial",
        subtitles: youtubeSubtitles,
      } satisfies SubtitleResponse,
      200,
      {
        // "Cache-Control": "public, max-age=3600, stale-while-revalidate=86400",
      },
    );
  })
  .get("/video/:video_id/metadata", async (c) => {
    const videoId = c.req.param("video_id");

    logger.info("Fetching video metadata", { videoId });

    // Access services from context variables
    const { services, createRevalidator } = c.var;
    const revalidator = createRevalidator(videoId);
    const [cachedMetadata, cachedApiMetadata] = await Promise.all([
      services.cache.getMetadata(videoId),
      services.cache.getYouTubeApiMetadata(videoId),
    ]);

    const cacheStatus = {
      ytDlpCached: !!cachedMetadata,
      ytDlpStale: cachedMetadata?.isStale ?? false,
      apiCached: !!cachedApiMetadata,
      apiStale: cachedApiMetadata?.isStale ?? false,
    };

    logger.debug("Metadata cache status", { videoId, cacheStatus });

    if (cachedMetadata?.isStale) {
      logger.debug("Triggering yt-dlp metadata revalidation", {
        videoId,
        reason: "stale_cache",
      });
      revalidator.revalidateWithYtDlp();
    }

    let youtubeApiMetadata: YouTubeApiMetadata | null;
    if (!cachedApiMetadata) {
      logger.debug("Fetching fresh YouTube API metadata", { videoId });
      // if youtube api metadata is not cached, fetch and update cache
      youtubeApiMetadata = await services.youtubeApi.getVideoMetadata(videoId);

      if (!youtubeApiMetadata) {
        logger.warn("YouTube API metadata not found", { videoId });
        return c.text("Not found", 404);
      }

      logger.debug("Background caching YouTube API metadata", { videoId });
      c.executionCtx.waitUntil(
        services.cache.setYouTubeApiMetadata(videoId, youtubeApiMetadata),
      );
    } else {
      youtubeApiMetadata = cachedApiMetadata.data;
      if (cachedApiMetadata.isStale) {
        logger.debug("Triggering API metadata revalidation", { videoId });
        revalidator.revalidateApiMetadata();
      }
    }

    if (!cachedMetadata) {
      logger.debug("Triggering yt-dlp metadata revalidation", {
        videoId,
        reason: "no_cache",
      });
      revalidator.revalidateWithYtDlp();
    }

    const mergedResponse = mergeMetadataResponse({
      ytDlp: cachedMetadata?.data,
      youtubeApi: youtubeApiMetadata,
    });

    logger.info("Returning video metadata", {
      videoId,
      type: mergedResponse.type,
      hasYtDlpData: !!cachedMetadata,
      title: youtubeApiMetadata.title,
    });

    return c.json(mergedResponse, 200, {
      // "Cache-Control": "public, max-age=7200, stale-while-revalidate=86400",
    });
  })
  .get("/video/:video_id/subtitles/:subtitle_id", async (c) => {
    const videoId = c.req.param("video_id");
    const subtitleId = c.req.param("subtitle_id");

    logger.info("Fetching subtitle content", { videoId, subtitleId });

    const { services, createRevalidator } = c.var;
    const revalidator = createRevalidator(videoId);

    // Check if we have cached subtitle URL
    const cachedSubtitle = await services.cache.getSubtitleUrl(
      videoId,
      subtitleId,
    );

    logger.debug("Subtitle URL cache check", {
      videoId,
      subtitleId,
      cached: !!cachedSubtitle,
      stale: cachedSubtitle?.isStale ?? false,
    });

    // we don't trigger revalidation if there's no cached subtitle, since may 404
    if (cachedSubtitle?.isStale) {
      logger.debug("Triggering yt-dlp revalidation for subtitle", {
        videoId,
        subtitleId,
        reason: "stale_cache",
      });
      revalidator.revalidateWithYtDlp();
    }

    if (cachedSubtitle) {
      logger.debug("Processing subtitle from cached URL", {
        videoId,
        subtitleId,
      });

      const fileContent = await processYouTubeJson3Subtitle(
        cachedSubtitle.url,
        { videoId, subtitleId },
      );

      logger.info("Returning subtitle content", {
        videoId,
        subtitleId,
        contentLength: fileContent.length,
      });

      return c.text(fileContent, 200, {
        "Content-Type": "text/vtt",
      });
    }

    // No cached subtitle URL - check if subtitle exists in the cached subtitle list
    const hasSubtitle = await services.cache.hasSubtitle(videoId, subtitleId);

    logger.debug("Subtitle existence check", {
      videoId,
      subtitleId,
      hasSubtitle,
    });

    if (hasSubtitle === null) {
      logger.info("Subtitle processing in progress", { videoId, subtitleId });
      // trigger revalidation to update subtitle list
      logger.debug("Triggering yt-dlp revalidation for subtitle", {
        videoId,
        subtitleId,
        reason: "no_cache",
      });
      revalidator.revalidateWithYtDlp();

      // No subtitle list cached - processing in progress
      return c.text("Processing", 202);
    }

    if (hasSubtitle === false) {
      logger.warn("Subtitle not found", { videoId, subtitleId });
      return c.text("Not found", 404);
    }

    if (hasSubtitle === true) {
      logger.error("Subtitle exists but URL not cached", {
        videoId,
        subtitleId,
      });
      // Subtitle exists in list but URL not cached - return error since URLs should be cached with subtitle list
      return c.text("Internal server error", 500);
    }

    assertNever(hasSubtitle);
  })
  .post(
    "/webhook/success",
    vValidator("json", webhookResponseSchema.success),
    async (c) => {
      const { lockId, videoUrl, runId, datasetId } = c.req.valid("json");

      logger.info("Processing webhook success", {
        lockId,
        videoUrl,
        runId,
        datasetId,
      });

      // Extract videoId from videoUrl
      const videoId = new URL(videoUrl).searchParams.get("v");
      if (!videoId) {
        logger.error("Invalid video URL in webhook", { videoUrl });
        return c.text("Invalid video URL", 400);
      }

      // Access services from context variables
      const { services } = c.var;

      try {
        logger.debug("Fetching run result from Apify", { datasetId, videoId });
        // Process the dataset data
        const data = await services.apify.getRunResult(datasetId);

        logger.debug("Updating cache with yt-dlp data", {
          videoId,
          subtitleCount: data.subtitles.length,
          hasMetadata: !!data.metadata,
        });

        const pipeline = services.cache.pipeline();
        const ctx = { redis: pipeline };
        // Update all cache entries since yt-dlp provides complete data
        services.cache.setMetadata(videoId, data.metadata, ctx);
        services.cache.setSubtitleList(videoId, data.subtitles, ctx);
        // Cache subtitle URLs using hash-based storage with shared expiration
        services.cache.setSubtitleUrlsHash(videoId, data.subtitles, ctx);
        await pipeline.exec();

        const lock = {
          key: LOCK_KEYS.ytdlp(videoId),
          id: lockId,
        };

        logger.debug("Releasing processing lock", { lock, videoId });
        // Release the lock
        await services.cache.releaseLock(lock);

        logger.debug("Background cleanup of dataset", { datasetId });
        c.executionCtx.waitUntil(services.apify.cleanupDataset(datasetId));

        logger.info("Webhook success processing completed", {
          videoId,
          lockId,
        });

        return c.text("Success :D", 200);
      } catch (error) {
        logger.error("Webhook success processing failed", {
          videoId,
          lockId,
          error: error instanceof Error ? error.message : String(error),
        });

        // Don't release lock if webhook have trouble processing
        // await services.cache.releaseLock(
        //   lockId,
        //   videoId,
        //   "failed",
        //   error instanceof Error ? error.message : String(error),
        // );

        return c.text("Processing failed", 500);
      }
    },
  )
  .post(
    "/webhook/failed",
    vValidator("json", webhookResponseSchema.failed),
    async (c) => {
      const { lockId, videoUrl, runId, status, error } = c.req.valid("json");

      logger.warn("Processing webhook failure", {
        lockId,
        videoUrl,
        runId,
        status,
        error,
      });

      // Extract videoId from videoUrl
      const videoId = new URL(videoUrl).searchParams.get("v");
      if (!videoId) {
        logger.error("Invalid video URL in failed webhook", { videoUrl });
        return c.text("Invalid video URL", 400);
      }

      // Access services from context variables
      const { services } = c.var;

      try {
        logger.debug("Releasing lock with failed status", {
          lockId,
          videoId,
          error,
        });

        const lock = {
          key: LOCK_KEYS.ytdlp(videoId),
          id: lockId,
        };

        // Release the lock with error status
        await services.cache.releaseLock(lock);

        logger.info("Webhook failure processing completed", {
          videoId,
          lock,
        });

        return c.text("Success", 200);
      } catch (processingError) {
        logger.error("Webhook failure processing failed", {
          videoId,
          lockId,
          processingError:
            processingError instanceof Error
              ? processingError.message
              : String(processingError),
          originalError: error,
        });

        return c.text("Processing failed", 500);
      }
    },
  );

export default app;

function mergeMetadataResponse({
  ytDlp,
  youtubeApi,
}: {
  ytDlp?: YtDlpYouTubeMetadata;
  youtubeApi: YouTubeApiMetadata;
}) {
  const basic = pick(youtubeApi, [
    "title",
    "description",
    "tags",
    "duration",
    "published_at",
    "uploader_name",
    "thumbnails",
    "view_count",
    "like_count",
    "comment_count",
  ]);
  if (!ytDlp)
    return {
      type: "partial",
      ...basic,
    };
  return {
    type: "full",
    language: youtubeApi.language,
    aspect_ratio: ytDlp.aspect_ratio,
    chapters: ytDlp.chapters,
    uploader_id: ytDlp.uploader_id,
    ...basic,
  };
}
